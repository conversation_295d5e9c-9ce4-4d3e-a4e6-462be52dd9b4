package main

import (
	"fmt"
	"math/rand"
	"os/exec"
	"strings"
	"time"

	"github.com/charmbracelet/bubbles/list"
	textinput "github.com/charmbracelet/bubbles/textinput"
	tea "github.com/charmbracelet/bubbletea"
	"github.com/charmbracelet/lipgloss"
)

const (
	numPorts       = 16
	pulseInterval  = 500 * time.Millisecond
	sparkleSteps   = 5 // For results animation
	glitchInterval = 200 * time.Millisecond
	maxFrames      = 25 // ~5 seconds for splash
)

type state int

const (
	splashState state = iota
	mainState
)

type testResultMsg struct {
	output string
	err    error
}

type tickMsg time.Time

type model struct {
	state        state
	splash       splashModel
	main         mainModel
	windowWidth  int
	windowHeight int
}

type splashModel struct {
	frames int
}

type mainModel struct {
	machineName   textinput.Model
	testList      list.Model
	ports         []bool // Active ports 0-15 (1-16)
	results       string
	pulseStep     int
	animationStep int
	quitting      bool
}

var (
	// Splash styles
	baseAscii = []string{
		"   ___    ____   _   _  _____ _____ ",
		"  / _ \\  | __ ) | | | |/ ___| ____|",
		" | | | | |  _ \\ | | | | |  _|  _|  ",
		" | |_| | | |_) | | |_| | | | | |___ ",
		"  \\___/  |____/   \\___/ |__| |_____|",
	}
	glitchChars = []rune{'@', '#', '*', '~', '?', '!', '%', '&'}
	splashStyle = lipgloss.NewStyle().
			Foreground(lipgloss.Color("#00FFFF")).
			Bold(true).
			Align(lipgloss.Center)

	// Main TUI styles
	baseMainStyle = lipgloss.NewStyle().
			Border(lipgloss.DoubleBorder()).
			BorderForeground(lipgloss.AdaptiveColor{Light: "#FF00FF", Dark: "#4B0082"}).
			Padding(1, 2)

	baseInputStyle = lipgloss.NewStyle().
			Border(lipgloss.RoundedBorder()).
			BorderForeground(lipgloss.Color("#FFD700")).
			Padding(0, 1)

	baseListStyle = lipgloss.NewStyle().
			Border(lipgloss.NormalBorder()).
			BorderForeground(lipgloss.Color("#1E90FF")).
			Padding(1)

	inactivePortStyle = lipgloss.NewStyle().
				Border(lipgloss.ThickBorder()).
				BorderForeground(lipgloss.Color("#333333")).
				Width(4).
				Height(1).
				Align(lipgloss.Center).
				Background(lipgloss.Color("#333333"))

	activePortStyle1 = inactivePortStyle.Copy().
				BorderForeground(lipgloss.Color("#00FF00")).
				Background(lipgloss.AdaptiveColor{Light: "#00FF00", Dark: "#00008B"})

	activePortStyle2 = inactivePortStyle.Copy().
				BorderForeground(lipgloss.Color("#00FFFF")).
				Background(lipgloss.AdaptiveColor{Light: "#00FFFF", Dark: "#008B8B"})

	baseResultsStyle = lipgloss.NewStyle().
				Border(lipgloss.DoubleBorder()).
				BorderForeground(lipgloss.Color("#FFA500")).
				Background(lipgloss.AdaptiveColor{Light: "#FFD700", Dark: "#FFA500"}).
				Padding(1)

	passStyle = lipgloss.NewStyle().Foreground(lipgloss.Color("#00FF00")).Bold(true)
	failStyle = lipgloss.NewStyle().Foreground(lipgloss.Color("#FF0000")).Bold(true)

	portGridStyle = lipgloss.NewStyle().
			Border(lipgloss.NormalBorder()).
			BorderForeground(lipgloss.Color("#00FFFF")).
			Padding(0, 1)
)

func initialModel() model {
	ti := textinput.New()
	ti.Placeholder = "machine-name-1"
	ti.Focus()

	tests := []list.Item{
		item{title: "BERT Test"},
		item{title: "Reflection Test"},
		item{title: "Link Quality Test"},
	}
	testList := list.New(tests, list.NewDefaultDelegate(), 0, 0)
	testList.Title = "Select Test"

	return model{
		state:  splashState,
		splash: splashModel{},
		main: mainModel{
			machineName: ti,
			testList:    testList,
			ports:       make([]bool, numPorts),
			results:     "Results will appear here...",
		},
		windowWidth:  80,
		windowHeight: 24,
	}
}

type item struct {
	title string
}

func (i item) Title() string       { return i.title }
func (i item) Description() string { return "" }
func (i item) FilterValue() string { return i.title }

func (m model) Init() tea.Cmd {
	return tickCmd()
}

func (m model) Update(msg tea.Msg) (tea.Model, tea.Cmd) {
	var cmds []tea.Cmd

	// Handle window size separately to avoid unused msg
	if wsm, ok := msg.(tea.WindowSizeMsg); ok {
		m.windowWidth = wsm.Width
		m.windowHeight = wsm.Height
		if m.state == mainState {
			m.main.machineName.Width = max(20, m.windowWidth/3)
			m.main.testList.SetWidth(max(20, m.windowWidth/4))
			m.main.testList.SetHeight(max(10, m.windowHeight/4))
			if m.windowHeight < 20 {
				m.main.results = "Terminal too small! Please resize to at least 20 lines."
			}
		}
		return m, nil
	}

	switch m.state {
	case splashState:
		switch msg := msg.(type) {
		case tea.KeyMsg:
			// Any key skips to main
			m.state = mainState
			return m, m.main.Init()
		case tickMsg:
			m.splash.frames++
			if m.splash.frames >= maxFrames {
				m.state = mainState
				return m, m.main.Init()
			}
			return m, tickCmd()
		}
	case mainState:
		var mainCmd tea.Cmd
		m.main, mainCmd = m.main.Update(msg)
		cmds = append(cmds, mainCmd)
	}

	return m, tea.Batch(cmds...)
}

func (m model) View() string {
	switch m.state {
	case splashState:
		return m.splashView()
	case mainState:
		return m.mainView()
	}
	return ""
}

func (m model) splashView() string {
	// Glitch the ASCII
	glitched := glitchAscii(baseAscii)

	// Join lines and style
	content := strings.Join(glitched, "\n")
	styled := splashStyle.Width(m.windowWidth).Render(content)

	// Center vertically
	padding := strings.Repeat("\n", (m.windowHeight-len(baseAscii))/2)
	return padding + styled
}

func glitchAscii(lines []string) []string {
	glitched := make([]string, len(lines))
	copy(glitched, lines)
	for i := range glitched {
		// 20% chance to shift line
		if rand.Float32() < 0.2 {
			shift := rand.Intn(3) - 1 // -1, 0, 1
			if shift > 0 {
				glitched[i] = " " + glitched[i][:len(glitched[i])-1]
			} else if shift < 0 {
				glitched[i] = glitched[i][1:] + " "
			}
		}
		// Replace random chars (8%)
		lineRunes := []rune(glitched[i])
		for j := range lineRunes {
			if rand.Float32() < 0.08 {
				lineRunes[j] = glitchChars[rand.Intn(len(glitchChars))]
			}
		}
		glitched[i] = string(lineRunes)
	}
	return glitched
}

func (m mainModel) Init() tea.Cmd {
	return tea.Batch(textinput.Blink, tickCmd())
}

func (m mainModel) Update(msg tea.Msg) (mainModel, tea.Cmd) {
	var cmds []tea.Cmd

	switch msg := msg.(type) {
	case tea.KeyMsg:
		switch msg.String() {
		case "ctrl+c", "q":
			m.quitting = true
			return m, tea.Quit
		case "enter":
			if m.machineName.Focused() {
				m.machineName.Blur()
				return m, nil
			}
			if selected, ok := m.testList.SelectedItem().(item); ok {
				m.results = "Running " + selected.title + "..."
				m.animationStep = sparkleSteps
				var args []string
				if m.machineName.Value() != "" {
					args = append(args, m.machineName.Value())
				}
				for i, active := range m.ports {
					if active {
						args = append(args, fmt.Sprintf("ICI%d", i+1))
					}
				}
				// Real command (uncomment for actual use; ensure 'ici-test' exists)
				// cmd := exec.Command("ici-test", args...)
				// Mock for demo
				cmd := exec.Command("echo", "Mock output: Pass for ICI1, Fail for ICI4")
				return m, tea.ExecProcess(cmd, func(err error) tea.Msg {
					if err != nil {
						return testResultMsg{err: err}
					}
					output, _ := cmd.Output()
					return testResultMsg{output: string(output)}
				})
			}
		case "tab":
			if m.machineName.Focused() {
				m.machineName.Blur()
			} else {
				m.machineName.Focus()
			}
			return m, nil
		default:
			key := msg.String()
			if idx := portKeyToIndex(key); idx >= 0 && idx < numPorts {
				m.ports[idx] = !m.ports[idx]
			}
		}
	case testResultMsg:
		if msg.err != nil {
			m.results = "Error: " + msg.err.Error()
		} else {
			m.results = parseOutput(msg.output)
		}
		return m, tickCmd()
	case tickMsg:
		m.pulseStep++
		if m.animationStep > 0 {
			m.animationStep--
		}
		cmds = append(cmds, tickCmd())
	}

	var cmd tea.Cmd
	m.machineName, cmd = m.machineName.Update(msg)
	cmds = append(cmds, cmd)
	m.testList, cmd = m.testList.Update(msg)
	cmds = append(cmds, cmd)

	return m, tea.Batch(cmds...)
}

func (m model) mainView() string {
	if m.main.quitting {
		return "Goodbye!\n"
	}

	mainStyle := baseMainStyle.Copy().
		Width(max(40, m.windowWidth-4))

	inputStyle := baseInputStyle.Copy().
		Width(max(20, m.windowWidth/3))

	listStyle := baseListStyle.Copy().
		Width(max(20, m.windowWidth/4))

	resultsStyle := baseResultsStyle.Copy().
		Width(max(30, m.windowWidth-8)).
		Height(max(8, m.windowHeight/3))

	var portRows []string
	for row := 0; row < 4; row++ {
		var rowPorts []string
		for col := 0; col < 4; col++ {
			idx := row*4 + col
			label := fmt.Sprintf("%2d", idx+1)
			style := inactivePortStyle
			if m.main.ports[idx] {
				if m.main.pulseStep%2 == 0 {
					style = activePortStyle1
				} else {
					style = activePortStyle2
				}
				style = style.BorderForeground(lipgloss.AdaptiveColor{Light: "#FFFFFF", Dark: "#999999"})
				if m.main.pulseStep%4 == 0 {
					label += "✦"
				}
			}
			rowPorts = append(rowPorts, style.Render(label))
		}
		portRows = append(portRows, lipgloss.JoinHorizontal(lipgloss.Top, rowPorts...))
	}
	grid := portGridStyle.Render(lipgloss.JoinVertical(lipgloss.Left, portRows...))

	inputView := inputStyle.Render(m.main.machineName.View())
	if m.main.machineName.Focused() && m.main.pulseStep%4 == 0 {
		inputView = lipgloss.NewStyle().Background(lipgloss.Color("#00FF00")).Render(inputView)
	}

	listView := m.main.testList.View()
	if selected := m.main.testList.SelectedItem(); selected != nil && m.main.animationStep > 0 {
		listView = lipgloss.NewStyle().Padding(0, 1).Render(listView)
	}
	listView = listStyle.Render(listView)

	resultsView := m.main.results
	if m.main.animationStep > 0 {
		resultsView += sparkleEffect(m.main.animationStep)
	}
	resultsView = resultsStyle.Render(resultsView)

	content := lipgloss.JoinVertical(
		lipgloss.Left,
		"ICI Test TUI - Futuristic Edition",
		"Machine Name: "+inputView,
		"Ports (toggle with 1-0/a-f):",
		grid,
		"Tests:",
		listView,
		"Results:",
		resultsView,
		"Enter to run, tab to switch focus, q to quit",
	)

	circuitStyle := lipgloss.NewStyle().Foreground(lipgloss.Color("#222222"))
	circuit := circuitStyle.Render("┌─┬┐\n│ Circuit Active │\n└─┴┘")
	content = lipgloss.JoinHorizontal(lipgloss.Left, content, circuit)

	return mainStyle.Render(content)
}

func portKeyToIndex(key string) int {
	switch key {
	case "1":
		return 0
	case "2":
		return 1
	case "3":
		return 2
	case "4":
		return 3
	case "5":
		return 4
	case "6":
		return 5
	case "7":
		return 6
	case "8":
		return 7
	case "9":
		return 8
	case "0":
		return 9
	case "a":
		return 10
	case "b":
		return 11
	case "c":
		return 12
	case "d":
		return 13
	case "e":
		return 14
	case "f":
		return 15
	}
	return -1
}

func parseOutput(output string) string {
	lines := strings.Split(output, "\n")
	var parsed []string
	for _, line := range lines {
		if strings.Contains(line, "Pass") {
			parsed = append(parsed, passStyle.Render(line))
		} else if strings.Contains(line, "Fail") {
			parsed = append(parsed, failStyle.Render(line))
		} else {
			parsed = append(parsed, line)
		}
	}
	return strings.Join(parsed, "\n")
}

func sparkleEffect(step int) string {
	var sparkles string
	for i := 0; i < step; i++ {
		if rand.Intn(2) == 0 {
			sparkles += "✨ "
		}
	}
	return "\n" + sparkles
}

func max(a, b int) int {
	if a > b {
		return a
	}
	return b
}

func main() {
	p := tea.NewProgram(initialModel())
	if _, err := p.Run(); err != nil {
		fmt.Println("Error running program:", err)
	}
}
