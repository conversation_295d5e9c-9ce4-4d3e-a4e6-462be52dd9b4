package main

import (
	"fmt"
	"math/rand"
	"os/exec"
	"strings"
	"time"

	"github.com/charmbracelet/bubbles/list"
	textinput "github.com/charmbracelet/bubbles/textinput"
	tea "github.com/charmbracelet/bubbletea"
	"github.com/charmbracelet/lipgloss"
)

const (
	numPorts      = 16
	pulseInterval = 500 * time.Millisecond
	sparkleSteps  = 5 // For results animation
)

type testResultMsg struct {
	output string
	err    error
}

type tickMsg time.Time

type model struct {
	machineName   textinput.Model
	testList      list.Model
	ports         []bool // Active ports 0-15 (1-16)
	results       string
	pulseStep     int
	animationStep int
	quitting      bool
	windowWidth   int
	windowHeight  int
}

var (
	// Base styles (copied and adjusted dynamically in View)
	baseMainStyle = lipgloss.NewStyle().
			Border(lipgloss.DoubleBorder()).
			BorderForeground(lipgloss.AdaptiveColor{Light: "#FF00FF", Dark: "#4B0082"}).
			Padding(1, 2)

	baseInputStyle = lipgloss.NewStyle().
			Border(lipgloss.RoundedBorder()).
			BorderForeground(lipgloss.Color("#FFD700")).
			Padding(0, 1)

	baseListStyle = lipgloss.NewStyle().
			Border(lipgloss.NormalBorder()).
			BorderForeground(lipgloss.Color("#1E90FF")).
			Padding(1)

	inactivePortStyle = lipgloss.NewStyle().
				Border(lipgloss.ThickBorder()).
				BorderForeground(lipgloss.Color("#333333")).
				Width(4).
				Height(1).
				Align(lipgloss.Center).
				Background(lipgloss.Color("#333333"))

	activePortStyle1 = inactivePortStyle.Copy().
				BorderForeground(lipgloss.Color("#00FF00")).
				Background(lipgloss.AdaptiveColor{Light: "#00FF00", Dark: "#00008B"})

	activePortStyle2 = inactivePortStyle.Copy().
				BorderForeground(lipgloss.Color("#00FFFF")).
				Background(lipgloss.AdaptiveColor{Light: "#00FFFF", Dark: "#008B8B"})

	baseResultsStyle = lipgloss.NewStyle().
				Border(lipgloss.DoubleBorder()).
				BorderForeground(lipgloss.Color("#FFA500")).
				Background(lipgloss.AdaptiveColor{Light: "#FFD700", Dark: "#FFA500"}).
				Padding(1)

	passStyle = lipgloss.NewStyle().Foreground(lipgloss.Color("#00FF00")).Bold(true)
	failStyle = lipgloss.NewStyle().Foreground(lipgloss.Color("#FF0000")).Bold(true)

	// Port grid container style for visibility
	portGridStyle = lipgloss.NewStyle().
			Border(lipgloss.NormalBorder()).
			BorderForeground(lipgloss.Color("#00FFFF")).
			Padding(0, 1)
)

func initialModel() model {
	ti := textinput.New()
	ti.Placeholder = "machine-name-1"
	ti.Focus()

	tests := []list.Item{
		item{title: "BERT Test"},
		item{title: "Reflection Test"},
		item{title: "Link Quality Test"},
	}
	testList := list.New(tests, list.NewDefaultDelegate(), 0, 0)
	testList.Title = "Select Test"

	return model{
		machineName:  ti,
		testList:     testList,
		ports:        make([]bool, numPorts),
		results:      "Results will appear here...",
		windowWidth:  80, // Initial fallback
		windowHeight: 24, // Initial fallback
	}
}

type item struct {
	title string
}

func (i item) Title() string       { return i.title }
func (i item) Description() string { return "" }
func (i item) FilterValue() string { return i.title }

func (m model) Init() tea.Cmd {
	return tea.Batch(textinput.Blink, tickCmd())
}

func tickCmd() tea.Cmd {
	return tea.Tick(pulseInterval, func(t time.Time) tea.Msg {
		return tickMsg(t)
	})
}

func (m model) Update(msg tea.Msg) (tea.Model, tea.Cmd) {
	var cmds []tea.Cmd

	switch msg := msg.(type) {
	case tea.KeyMsg:
		switch msg.String() {
		case "ctrl+c", "q":
			m.quitting = true
			return m, tea.Quit
		case "enter":
			if m.machineName.Focused() {
				m.machineName.Blur()
				return m, nil
			}
			// Run test if a test is selected
			if selected, ok := m.testList.SelectedItem().(item); ok {
				m.results = "Running " + selected.title + "..."
				m.animationStep = sparkleSteps
				// Construct command args
				var args []string
				if m.machineName.Value() != "" {
					args = append(args, m.machineName.Value())
				}
				for i, active := range m.ports {
					if active {
						args = append(args, fmt.Sprintf("ICI%d", i+1))
					}
				}
				// Real command (uncomment for actual use; ensure 'ici-test' exists)
				// cmd := exec.Command("ici-test", args...)
				// Mock for demo
				cmd := exec.Command("echo", "Mock output: Pass for ICI1, Fail for ICI4")
				return m, tea.ExecProcess(cmd, func(err error) tea.Msg {
					if err != nil {
						return testResultMsg{err: err}
					}
					output, _ := cmd.Output()
					return testResultMsg{output: string(output)}
				})
			}
		case "tab":
			if m.machineName.Focused() {
				m.machineName.Blur()
			} else {
				m.machineName.Focus()
			}
			return m, nil
		// Toggle ports with keys 1-0 (1-10), a-f (11-16)
		default:
			key := msg.String()
			if idx := portKeyToIndex(key); idx >= 0 && idx < numPorts {
				m.ports[idx] = !m.ports[idx]
			}
		}
	case testResultMsg:
		if msg.err != nil {
			m.results = "Error: " + msg.err.Error()
		} else {
			// Parse and color output
			m.results = parseOutput(msg.output)
		}
		return m, tickCmd()
	case tickMsg:
		m.pulseStep++
		if m.animationStep > 0 {
			m.animationStep--
		}
		cmds = append(cmds, tickCmd())
	case tea.WindowSizeMsg:
		m.windowWidth = msg.Width
		m.windowHeight = msg.Height
		// Adjust subcomponents dynamically
		m.machineName.Width = max(20, m.windowWidth/3)
		m.testList.SetWidth(max(20, m.windowWidth/4))
		m.testList.SetHeight(max(10, m.windowHeight/4))
		// Warn if terminal is too small
		if m.windowHeight < 20 {
			m.results = "Terminal too small! Please resize to at least 20 lines."
		}
	}

	// Update subcomponents
	var cmd tea.Cmd
	m.machineName, cmd = m.machineName.Update(msg)
	cmds = append(cmds, cmd)
	m.testList, cmd = m.testList.Update(msg)
	cmds = append(cmds, cmd)

	return m, tea.Batch(cmds...)
}

func (m model) View() string {
	if m.quitting {
		return "Goodbye!\n"
	}

	// Dynamically adjust styles based on window size
	mainStyle := baseMainStyle.Copy().
		Width(max(40, m.windowWidth-4)) // Remove height to allow natural flow

	inputStyle := baseInputStyle.Copy().
		Width(max(20, m.windowWidth/3))

	listStyle := baseListStyle.Copy().
		Width(max(20, m.windowWidth/4))

	resultsStyle := baseResultsStyle.Copy().
		Width(max(30, m.windowWidth-8)).
		Height(max(8, m.windowHeight/3))

	// Port grid: 4x4, wrapped in a styled container
	var portRows []string
	for row := 0; row < 4; row++ {
		var rowPorts []string
		for col := 0; col < 4; col++ {
			idx := row*4 + col
			label := fmt.Sprintf("%2d", idx+1)
			style := inactivePortStyle
			if m.ports[idx] {
				if m.pulseStep%2 == 0 {
					style = activePortStyle1
				} else {
					style = activePortStyle2
				}
				// Glow effect
				style = style.BorderForeground(lipgloss.AdaptiveColor{Light: "#FFFFFF", Dark: "#999999"})
				// Spark
				if m.pulseStep%4 == 0 {
					label += "✦"
				}
			}
			rowPorts = append(rowPorts, style.Render(label))
		}
		portRows = append(portRows, lipgloss.JoinHorizontal(lipgloss.Top, rowPorts...))
	}
	grid := portGridStyle.Render(lipgloss.JoinVertical(lipgloss.Left, portRows...))

	// Machine name with flash if focused
	inputView := inputStyle.Render(m.machineName.View())
	if m.machineName.Focused() && m.pulseStep%4 == 0 {
		inputView = lipgloss.NewStyle().Background(lipgloss.Color("#00FF00")).Render(inputView)
	}

	// Test list with bounce effect
	listView := m.testList.View()
	if selected := m.testList.SelectedItem(); selected != nil && m.animationStep > 0 {
		listView = lipgloss.NewStyle().Padding(0, 1).Render(listView) // Subtle bounce
	}
	listView = listStyle.Render(listView)

	// Results with sparkle
	resultsView := m.results
	if m.animationStep > 0 {
		resultsView += sparkleEffect(m.animationStep)
	}
	resultsView = resultsStyle.Render(resultsView)

	// Overall layout: Ports near the top
	content := lipgloss.JoinVertical(
		lipgloss.Left,
		"ICI Test TUI - Futuristic Edition",
		"Machine Name: "+inputView,
		"Ports (toggle with 1-0/a-f):",
		grid,
		"Tests:",
		listView,
		"Results:",
		resultsView,
		"Enter to run, tab to switch focus, q to quit",
	)

	// Add subtle circuit border effect
	circuitStyle := lipgloss.NewStyle().Foreground(lipgloss.Color("#222222"))
	circuit := circuitStyle.Render("┌─┬┐\n│ Circuit Active │\n└─┴┘")
	content = lipgloss.JoinHorizontal(lipgloss.Left, content, circuit)

	return mainStyle.Render(content)
}

// Helper: Map key to port index (1-0 for 1-10, a-f for 11-16)
func portKeyToIndex(key string) int {
	switch key {
	case "1":
		return 0
	case "2":
		return 1
	case "3":
		return 2
	case "4":
		return 3
	case "5":
		return 4
	case "6":
		return 5
	case "7":
		return 6
	case "8":
		return 7
	case "9":
		return 8
	case "0":
		return 9
	case "a":
		return 10
	case "b":
		return 11
	case "c":
		return 12
	case "d":
		return 13
	case "e":
		return 14
	case "f":
		return 15
	}
	return -1
}

// Simple parse: Color pass/fail
func parseOutput(output string) string {
	lines := strings.Split(output, "\n")
	var parsed []string
	for _, line := range lines {
		if strings.Contains(line, "Pass") {
			parsed = append(parsed, passStyle.Render(line))
		} else if strings.Contains(line, "Fail") {
			parsed = append(parsed, failStyle.Render(line))
		} else {
			parsed = append(parsed, line)
		}
	}
	return strings.Join(parsed, "\n")
}

// Sparkle effect: Add random sparkles
func sparkleEffect(step int) string {
	var sparkles string
	for i := 0; i < step; i++ {
		if rand.Intn(2) == 0 {
			sparkles += "✨ "
		}
	}
	return "\n" + sparkles
}

// Helper: Ensure minimum size to prevent layout issues
func max(a, b int) int {
	if a > b {
		return a
	}
	return b
}

func main() {
	p := tea.NewProgram(initialModel())
	if _, err := p.Run(); err != nil {
		fmt.Println("Error running program:", err)
	}
}
